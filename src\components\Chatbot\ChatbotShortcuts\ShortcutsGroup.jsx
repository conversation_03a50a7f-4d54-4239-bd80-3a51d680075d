import DButtonIcon from '../../Global/DButtonIcon';

const ShortcutsGroup = ({ title, children, icon, actionClick }) => {
  return (
    <div className="flex flex-col gap-size2">
      {(title || icon) && (
        <header className="flex justify-between">
          {title && <h2 className="text-lg">{title}</h2>}
          {icon && (
            <DButtonIcon
              onClick={() => actionClick()}
              variant="outlined"
              size="sm"
            >
              {icon}
            </DButtonIcon>
          )}
        </header>
      )}
      <div className="flex flex-col gap-size3">{children}</div>
    </div>
  );
};
export default ShortcutsGroup;
