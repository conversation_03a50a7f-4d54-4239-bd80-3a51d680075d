const ImageIcon = (props) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.5 0C0.671573 0 0 0.671573 0 1.5V12.5C0 13.3284 0.671573 14 1.5 14H12.5C13.3284 14 14 13.3284 14 12.5V1.5C14 0.671573 13.3284 0 12.5 0H1.5ZM1 1.5C1 1.22386 1.22386 1 1.5 1H12.5C12.7761 1 13 1.22386 13 1.5V12.5C13 12.7761 12.7761 13 12.5 13H1.5C1.22386 13 1 12.7761 1 12.5V1.5ZM8.51207 4.49275C8.42536 4.34824 8.27168 4.25712 8.10328 4.2504C7.93487 4.24367 7.77442 4.32224 7.67646 4.45938L6.16836 6.57072L5.13332 5.79445C5.01721 5.70736 4.86898 5.67503 4.72715 5.70585C4.58532 5.73667 4.46388 5.8276 4.39437 5.95502L2.22771 9.92724C2.14321 10.0822 2.14663 10.2702 2.23671 10.4219C2.32679 10.5736 2.49019 10.6667 2.66666 10.6667H11.3333C11.5135 10.6667 11.6797 10.5698 11.7684 10.413C11.8572 10.2563 11.8547 10.0639 11.7621 9.90942L8.51207 4.49275ZM6.68463 7.5684L8.04693 5.66118L10.4502 9.66667H3.50893L4.99558 6.94114L5.97777 7.67778C6.08547 7.75855 6.22119 7.79249 6.35423 7.7719C6.48727 7.75131 6.60639 7.67795 6.68463 7.5684Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default ImageIcon;
