@keyframes glow {
    0% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
    25% {
      box-shadow: 0 0 30px rgba(106, 90, 205, 0.5), 0 0 60px rgba(123, 104, 238, 0.4), 0 0 90px rgba(147, 112, 219, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(72, 61, 139, 0.5), 0 0 60px rgba(65, 105, 225, 0.4), 0 0 90px rgba(0, 0, 255, 0.3);
    }
    75% {
      box-shadow: 0 0 30px rgba(138, 43, 226, 0.5), 0 0 60px rgba(148, 0, 211, 0.4), 0 0 90px rgba(153, 50, 204, 0.3);
    }
    100% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
  }

  @keyframes glow<PERSON><PERSON>ald {
    0%, 25% {
      box-shadow: 0 0 15px rgba(128, 112, 242, 0.4), 0 0 30px rgba(147, 112, 219, 0.3), 0 0 45px rgba(138, 43, 226, 0.2);
    }
    100% {
      box-shadow: 0 0 30px rgba(51, 192, 142, 0.5), 0 0 60px rgba(51, 192, 142, 0.4), 0 0 90px rgba(51, 192, 142, 0.3);
    }
  }

  .glow-effect {
    animation: glow 4s ease-in-out infinite;
  }

  .glow-effect:hover {
    animation: glowEmerald 0.3s ease-in-out forwards;
  }

  /* Add styles for the icon gradient transition */
  .icon-transition stop {
    transition: stop-color 0.3s ease-in-out;
  }

  .glow-effect:hover .icon-transition stop:first-child {
    stop-color: rgb(51, 192, 142);
  }

  .glow-effect:hover .icon-transition stop:last-child {
    stop-color: rgb(51, 192, 142);
  }
