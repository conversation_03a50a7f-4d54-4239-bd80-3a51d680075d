import React from 'react';
import { DateTime } from 'luxon';

const DDateRangePicker = ({ fromDate, toDate, onFromChange, onToChange }) => {
  return (
    <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:space-x-4">
      <div className="flex flex-col w-full sm:w-auto">
        <input
          type="date"
          id="from-date"
          value={DateTime.fromISO(fromDate || DateTime.now()).toFormat(
            'yyyy-MM-dd'
          )}
          onChange={(e) => onFromChange(e.target.value)}
          className="dbutton p-2 border border-grey-5 rounded-md focus:outline-none bg-white hover:bg-grey-2 transition ease-in-out duration-150 text-sm text-min-safe-input"
        />
      </div>

      <div className="flex flex-col w-full sm:w-auto">
        <input
          type="date"
          id="to-date"
          value={DateTime.fromISO(toDate).toFormat('yyyy-MM-dd')}
          onChange={(e) => onToChange(e.target.value)}
          className="dbutton p-2 border border-grey-5 rounded-md focus:outline-none bg-white hover:bg-grey-2 transition ease-in-out duration-150 text-sm text-min-safe-input"
        />
      </div>
    </div>
  );
};

export default DDateRangePicker;
