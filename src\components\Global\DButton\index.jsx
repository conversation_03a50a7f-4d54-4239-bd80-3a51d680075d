import clsx from 'clsx';

import { But<PERSON> } from '@headlessui/react';

import <PERSON>pinner from '../DSpinner';

/**
 * DButton component renders a customizable button with various sizes, variants, and optional icons.
 *
 * @param {Object} props - The properties passed to the DButton component.
 * @param {string} [props.size='sm'] - Defines the size of the button (e.g., 'sm', 'md').
 * @param {boolean} [props.fullWidth=false] - Whether the button should take up the full width of its container.
 * @param {string} props.variant - Defines the style variant of the button (e.g., 'contained', 'outlined', 'dark', 'grey', etc.).
 * @param {function} props.onClick - Callback function triggered when the button is clicked.
 * @param {ReactNode} props.children - The content inside the button (e.g., text, icons).
 * @param {string} [props.className=''] - Additional CSS classes to apply to the button.
 * @returns {JSX.Element} The rendered button component.
 */
const DButton = ({
  size = 'sm',
  fullWidth = false,
  variant,
  onClick,
  children,
  className = '',
  loading = false,
  loadingText = '',
  disabled = false,
  ...props
}) => {
  const buttonSize = clsx(
    size === 'sm' && 'p-size1 text-base h-8',
    size === 'md' && 'p-size2 text-lg',
    size === 'lg' && 'p-size2 text-lg min-h-12',
    variant === 'squared' && 'h-8',
    fullWidth ? 'w-full' : 'w-max'
  );

  const buttonVariant = clsx(
    variant === 'contained' &&
      'rounded-[4px] bg-purple-200 text-[#fff] disabled:bg-purple-200/80',
    variant === 'outlined' &&
      'rounded-[4px] border border-gray-10 bg-transparent disabled:border-gray-10/20 disabled:text-grey-10',
    variant === 'dark' &&
      'rounded-[4px] bg-black text-white disabled:bg-black/20',
    variant === 'grey' &&
      'rounded-[4px] bg-grey-10 dark:bg-black text-black disabled:bg-white disabled:text-grey-10',
    variant === 'squared' && 'rounded-size1 border',
    variant === 'green' &&
      'rounded-[4px] bg-green-500 border border-green-500 text-[#fff] disabled:bg-green-500/20',
    variant === 'danger' &&
      'rounded-[4px] bg-negative-100 text-[#fff] disabled:bg-negative-100/20',
    variant === 'light' &&
      'rounded-size0 bg-white text-dark border border-gray-10 disabled:bg-white/20',
    variant === 'resolved' &&
      'rounded-size0 bg-green-50 border border-green-50 text-green-100 disabled:bg-green-50/20'
  );

  return (
    <Button
      data-testid={`d-button-${props.name || props.id}`}
      {...props}
      className={clsx(
        'dbutton',
        'flex items-center justify-center gap-2 font-medium tracking-tighter whitespace-nowrap rounded-[4px]',
        className,
        buttonSize,
        buttonVariant
      )}
      onClick={onClick}
      disabled={loading || disabled}
    >
      {loading ? (
        <div className="flex items-center gap-2">
          <DSpinner style={{ height: 26, width: 26 }} />
          {loadingText && <span>{loadingText}</span>}
        </div>
      ) : (
        children
      )}
    </Button>
  );
};

export default DButton;
