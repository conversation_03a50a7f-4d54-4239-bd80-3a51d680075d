import DInput from '@/components/Global/DInput/DInput';
import ReturnIcon from '@/components/Global/Icons/ReturnIcon';
import DButton from '@/components/Global/DButton';
import { useEffect, useState, useRef } from 'react';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import './index.css';

const ChatbotName = ({ error, chatbotData, setChatbotData, onNextClick }) => {
  const setCurrentStep = useChatbotStore((state) => state.setCurrentStep);
  const [placeholderText, setPlaceholderText] = useState('');
  const [isTyping, setIsTyping] = useState(true);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const typingSpeed = 100; // ms per character when typing
  const erasingSpeed = 50; // ms per character when erasing
  const pauseBeforeErasing = 1500; // ms to wait before erasing
  const pauseBeforeTyping = 500; // ms to wait before typing new suggestion

  // List of chatbot name suggestions
  const suggestions = [
    'Customer Service Assistant',
    'Educational Mentor',
    'Customer Support Assistant',
    'Personal Fitness Trainer',
    'IT Helpdesk Bot',
    'Travel Planning Guide',
    'Recipe Recommendation Bot',
    'Language Learning Assistant',
    'Financial Advisor Bot',
  ];

  useEffect(() => {
    let timeout;

    // Typing effect
    if (isTyping) {
      if (placeholderText.length < suggestions[currentSuggestionIndex].length) {
        timeout = setTimeout(() => {
          setPlaceholderText(
            suggestions[currentSuggestionIndex].slice(
              0,
              placeholderText.length + 1
            )
          );
        }, typingSpeed);
      } else {
        // Finished typing current suggestion
        timeout = setTimeout(() => {
          setIsTyping(false);
        }, pauseBeforeErasing);
      }
    }
    // Erasing effect
    else {
      if (placeholderText.length > 0) {
        timeout = setTimeout(() => {
          setPlaceholderText(placeholderText.slice(0, -1));
        }, erasingSpeed);
      } else {
        // Finished erasing, move to next suggestion
        timeout = setTimeout(() => {
          const nextIndex = (currentSuggestionIndex + 1) % suggestions.length;
          setCurrentSuggestionIndex(nextIndex);
          setIsTyping(true);
        }, pauseBeforeTyping);
      }
    }

    return () => clearTimeout(timeout);
  }, [placeholderText, isTyping, currentSuggestionIndex]);

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && chatbotData.chatbotName) {
      onNextClick();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [chatbotData.chatbotName, onNextClick]);

  const handleFocus = () => {
    setIsInputFocused(true);
  };

  const handleBlur = () => {
    setIsInputFocused(false);
  };

  return (
    <div className="py-size5">
      <div className="flex justify-end px-size5">
        <DButton
          variant="contained"
          size="md"
          fullWidth
          onClick={onNextClick}
          disabled={!chatbotData.chatbotName}
          className="!h-[42px] !w-[308px] !rounded-none !text-base !gap-size0 !px-size2 !px-size3 disabled:border disabled:border-black/10 transition-all duration-200"
        >
          <div className="flex items-center justify-center gap-1">
            <span className="text-lg ml-size2">Next</span>
            <ChevronRightIcon className="w-5 h-5" />
          </div>
        </DButton>
      </div>
      <div className="flex flex-col gap-size5 max-w-[650px] justify-center mx-auto pt-size5 px-size5">
        <div className="flex flex-col gap-size0">
          <span className="text-xl font-medium tracking-tight">
            Set AI Chatbot name
          </span>
          <span className="text-xs text-grey-50 tracking-tight">
            Set a unique name and watch your AI Chatbot update on the right side
            of the screen.
          </span>
        </div>
        <div className="flex flex-col gap-size1">
          <div className="glow-effect-name bg-surface-100 rounded-size1 typewriter-container">
            <DInput
              value={chatbotData.chatbotName}
              onChange={(e) => setChatbotData('chatbotName', e.target.value)}
              placeholder=""
              error={error}
              inputClassName="typewriter-input"
              onFocus={handleFocus}
              onBlur={handleBlur}
            />
            {!chatbotData.chatbotName && (
              <div
                className={`typewriter-placeholder ${
                  !isInputFocused ? 'cursor-blink' : ''
                }`}
              >
                {placeholderText}
              </div>
            )}
          </div>
          <div className="flex items-center gap-1 mt-1 text-xs text-grey-50">
            <span>Click Next or hit Enter to proceed</span>
            <ReturnIcon className="w-4 h-4" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatbotName;
